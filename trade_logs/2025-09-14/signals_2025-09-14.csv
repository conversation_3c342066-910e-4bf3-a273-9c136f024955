timestamp,signal_type,symbol,action,price,stop_loss,entry_price,exit_price,current_price,raw_signal
2025-09-14T15:05:03.417298,TRADE,NIFTY,BUY,155.4,151.2,155.4,,,"TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)"
2025-09-14T15:11:59.151275,TRADE,NIFTY,BUY,155.4,151.2,155.4,,,"TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)"
2025-09-14T15:14:07.458463,TRADE,NIFTY,CLOSE,154.65,,,154.65,,"TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)"
2025-09-14T15:19:40.746454,TRADE,NIFTY,BUY,155.4,151.2,155.4,,,"TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)"
2025-09-14T15:19:40.896662,TRADE,NIFTY,CLOSE,154.65,,,154.65,,"TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)"
2025-09-14T15:38:04.505973,UNKNOWN,NIFTY,BUY,,,,,,"TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 15, 38, 4, 505553), quantity=1, price=None, stop_loss=None, entry_price=None, exit_price=None, current_price=None, exit_reason=None, signal_type=None, expiry_date=None, update_type=None, new_stop_loss=None)"
2025-09-14T15:38:44.458693,UNKNOWN,NIFTY,BUY,,,,,,"TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 15, 38, 44, 458210), quantity=1, price=None, stop_loss=None, entry_price=None, exit_price=None, current_price=None, exit_reason=None, signal_type=None, expiry_date=None, update_type=None, new_stop_loss=None)"
